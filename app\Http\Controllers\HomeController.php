<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\MenuItem;
use App\Models\RestaurantInfo;


class HomeController extends Controller
{
    public function index()
    {
        $featuredMenus = MenuItem::with('category')
            ->active()
            ->featured()
            ->ordered()
            ->take(6)
            ->get();

        $categories = Category::active()->ordered()->get();
        $restaurantInfo = RestaurantInfo::getInfo();

        return view('home', compact('featuredMenus', 'categories', 'restaurantInfo'));
    }

    public function menu()
    {
        $categories = Category::active()->ordered()->get();
        $featuredMenus = MenuItem::active()->featured()->with('category')->ordered()->take(6)->get();
        $menuItems = MenuItem::active()->with('category')->ordered()->paginate(12);

        return view('menu.index', compact('categories', 'featuredMenus', 'menuItems'));
    }

    public function menuByCategory($categorySlug)
    {
        $categories = Category::active()->ordered()->get();
        $currentCategory = Category::where('slug', $categorySlug)->active()->firstOrFail();

        $featuredMenus = MenuItem::where('category_id', $currentCategory->id)
            ->active()
            ->featured()
            ->with('category')
            ->ordered()
            ->take(6)
            ->get();

        $menuItems = MenuItem::where('category_id', $currentCategory->id)
            ->active()
            ->with('category')
            ->ordered()
            ->paginate(12);

        return view('menu.index', compact('categories', 'currentCategory', 'featuredMenus', 'menuItems'));
    }

    public function about()
    {
        // Static about page data (since we removed AboutPage model)
        $aboutPage = (object) [
            'title' => 'เกี่ยวกับเรา',
            'description' => 'ร้านอาหารที่มีประวัติยาวนาน',
            'hero_image' => null,
            'default_background' => null,
            'content' => 'ยินดีต้อนรับสู่ร้านอาหารของเรา',
            'mission' => 'มุ่งมั่นให้บริการอาหารคุณภาพดี',
            'vision' => 'เป็นร้านอาหารที่ลูกค้าไว้วางใจ',
            'values' => 'คุณภาพ ความสะอาด การบริการ',
            'history' => 'ก่อตั้งขึ้นด้วยความรักในการทำอาหาร',
            'team_description' => 'ทีมงานมืออาชีพ',
            'chef_image' => null,
            'chef_name' => null,
            'chef_description' => null,
            'restaurant_images' => null,
            'awards' => null,
            'certifications' => null
        ];

        $restaurantInfo = RestaurantInfo::getInfo();
        return view('about', compact('aboutPage', 'restaurantInfo'));
    }
}
